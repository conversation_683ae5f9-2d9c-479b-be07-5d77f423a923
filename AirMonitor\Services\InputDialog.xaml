<Window x:Class="AirMonitor.Services.InputDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:hc="https://handyorg.github.io/handycontrol"
        Title="输入" Height="220" Width="450"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        ShowInTaskbar="False"
        Style="{StaticResource WindowWin10}">

    <hc:Card Margin="10">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="20"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="20"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <TextBlock x:Name="MessageTextBlock"
                       Grid.Row="0"
                       TextWrapping="Wrap"
                       VerticalAlignment="Center"
                       FontSize="14"
                       Foreground="{DynamicResource PrimaryTextBrush}"/>

            <hc:TextBox x:Name="InputTextBox"
                        Grid.Row="2"
                        Height="32"
                        hc:InfoElement.Placeholder="请输入内容..."
                        TextChanged="InputTextBox_TextChanged"/>

            <hc:UniformSpacingPanel Grid.Row="4"
                                    Orientation="Horizontal"
                                    HorizontalAlignment="Right"
                                    Spacing="10">
                <Button Name="OkButton"
                        Content="确定"
                        Width="80"
                        Height="32"
                        IsDefault="True"
                        Style="{StaticResource ButtonPrimary}"
                        Click="OkButton_Click"/>
                <Button Name="CancelButton"
                        Content="取消"
                        Width="80"
                        Height="32"
                        IsCancel="True"
                        Click="CancelButton_Click"/>
            </hc:UniformSpacingPanel>
        </Grid>
    </hc:Card>
</Window>
