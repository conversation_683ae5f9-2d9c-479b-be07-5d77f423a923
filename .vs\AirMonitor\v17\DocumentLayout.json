{"Version": 1, "WorkspaceRootPath": "D:\\01 AirMonitor\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{FFCDBA09-2229-4658-B8A4-EA919C493D3D}|AirMonitor\\AirMonitor.csproj|d:\\01 airmonitor\\airmonitor\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FFCDBA09-2229-4658-B8A4-EA919C493D3D}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FFCDBA09-2229-4658-B8A4-EA919C493D3D}|AirMonitor\\AirMonitor.csproj|d:\\01 airmonitor\\airmonitor\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{FFCDBA09-2229-4658-B8A4-EA919C493D3D}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{FFCDBA09-2229-4658-B8A4-EA919C493D3D}|AirMonitor\\AirMonitor.csproj|d:\\01 airmonitor\\airmonitor\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{FFCDBA09-2229-4658-B8A4-EA919C493D3D}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{FFCDBA09-2229-4658-B8A4-EA919C493D3D}|AirMonitor\\AirMonitor.csproj|d:\\01 airmonitor\\airmonitor\\services\\inputdialog.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{FFCDBA09-2229-4658-B8A4-EA919C493D3D}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\services\\inputdialog.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 8, "Children": [{"$type": "Bookmark", "Name": "ST:130:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:132:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:131:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:133:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:134:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:135:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d84ee353-0bef-5a41-a649-8f89aca5d84d}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "MainWindow.xaml.cs", "DocumentMoniker": "D:\\01 AirMonitor\\AirMonitor\\MainWindow.xaml.cs", "RelativeDocumentMoniker": "AirMonitor\\MainWindow.xaml.cs", "ToolTip": "D:\\01 AirMonitor\\AirMonitor\\MainWindow.xaml.cs", "RelativeToolTip": "AirMonitor\\MainWindow.xaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T03:13:21.879Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "InputDialog.xaml", "DocumentMoniker": "D:\\01 AirMonitor\\AirMonitor\\Services\\InputDialog.xaml", "RelativeDocumentMoniker": "AirMonitor\\Services\\InputDialog.xaml", "ToolTip": "D:\\01 AirMonitor\\AirMonitor\\Services\\InputDialog.xaml", "RelativeToolTip": "AirMonitor\\Services\\InputDialog.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T03:10:35.247Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "MainWindow.xaml", "DocumentMoniker": "D:\\01 AirMonitor\\AirMonitor\\MainWindow.xaml", "RelativeDocumentMoniker": "AirMonitor\\MainWindow.xaml", "ToolTip": "D:\\01 AirMonitor\\AirMonitor\\MainWindow.xaml", "RelativeToolTip": "AirMonitor\\MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T03:08:32.671Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "App.xaml", "DocumentMoniker": "D:\\01 AirMonitor\\AirMonitor\\App.xaml", "RelativeDocumentMoniker": "AirMonitor\\App.xaml", "ToolTip": "D:\\01 AirMonitor\\AirMonitor\\App.xaml", "RelativeToolTip": "AirMonitor\\App.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T03:07:51.093Z", "EditorCaption": ""}]}]}]}