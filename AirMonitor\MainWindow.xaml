﻿<Window
    x:Class="AirMonitor.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:local="clr-namespace:AirMonitor"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="{Binding Title}"
    Width="1000"
    Height="700"
    WindowStartupLocation="CenterScreen"
    Style="{StaticResource WindowWin10}"
    mc:Ignorable="d">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <hc:SimplePanel Grid.Row="0" Background="{DynamicResource PrimaryBrush}" Height="60">
            <TextBlock Text="{Binding Title}"
                       FontSize="18"
                       FontWeight="Bold"
                       Foreground="White"
                       VerticalAlignment="Center"
                       Margin="20,0"/>
        </hc:SimplePanel>

        <!-- 主内容区域 -->
        <hc:TabControl Grid.Row="1"
                       Margin="10"
                       Style="{StaticResource TabControlInLine}">

            <!-- 仪表板页面 -->
            <hc:TabItem Header="仪表板" Style="{StaticResource TabItemInLine}">
                <Grid Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="20"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 欢迎信息 -->
                    <hc:Card Grid.Row="0" Padding="20">
                        <StackPanel>
                            <TextBlock Text="欢迎使用 AirMonitor"
                                       FontSize="24"
                                       FontWeight="Bold"
                                       Margin="0,0,0,10"/>
                            <TextBlock Text="基于 HandyControl 的现代化 WPF MVVM 应用程序框架"
                                       FontSize="14"
                                       Foreground="{DynamicResource SecondaryTextBrush}"/>
                        </StackPanel>
                    </hc:Card>

                    <!-- 功能卡片 -->
                    <hc:UniformSpacingPanel Grid.Row="2"
                                            Spacing="20"
                                            Orientation="Horizontal">

                        <!-- 系统状态卡片 -->
                        <hc:Card Width="200" Height="150">
                            <StackPanel Margin="15" VerticalAlignment="Center">
                                <hc:SimplePanel Height="40" Margin="0,0,0,10">
                                    <Ellipse Width="40" Height="40"
                                             Fill="{DynamicResource SuccessBrush}"/>
                                    <Path Data="{StaticResource CheckedGeometry}"
                                          Fill="White"
                                          Width="20" Height="20"/>
                                </hc:SimplePanel>
                                <TextBlock Text="系统状态"
                                           FontWeight="Bold"
                                           HorizontalAlignment="Center"
                                           Margin="0,0,0,5"/>
                                <TextBlock Text="运行正常"
                                           Foreground="{DynamicResource SuccessBrush}"
                                           HorizontalAlignment="Center"/>
                            </StackPanel>
                        </hc:Card>

                        <!-- 数据监控卡片 -->
                        <hc:Card Width="200" Height="150">
                            <StackPanel Margin="15" VerticalAlignment="Center">
                                <hc:SimplePanel Height="40" Margin="0,0,0,10">
                                    <Ellipse Width="40" Height="40"
                                             Fill="{DynamicResource InfoBrush}"/>
                                    <Path Data="{StaticResource InfoGeometry}"
                                          Fill="White"
                                          Width="20" Height="20"/>
                                </hc:SimplePanel>
                                <TextBlock Text="数据监控"
                                           FontWeight="Bold"
                                           HorizontalAlignment="Center"
                                           Margin="0,0,0,5"/>
                                <TextBlock Text="实时更新"
                                           Foreground="{DynamicResource InfoBrush}"
                                           HorizontalAlignment="Center"/>
                            </StackPanel>
                        </hc:Card>

                        <!-- 日志管理卡片 -->
                        <hc:Card Width="200" Height="150">
                            <StackPanel Margin="15" VerticalAlignment="Center">
                                <hc:SimplePanel Height="40" Margin="0,0,0,10">
                                    <Ellipse Width="40" Height="40"
                                             Fill="{DynamicResource WarningBrush}"/>
                                    <Path Data="{StaticResource DocumentGeometry}"
                                          Fill="White"
                                          Width="20" Height="20"/>
                                </hc:SimplePanel>
                                <TextBlock Text="日志管理"
                                           FontWeight="Bold"
                                           HorizontalAlignment="Center"
                                           Margin="0,0,0,5"/>
                                <TextBlock Text="查看日志"
                                           Foreground="{DynamicResource WarningBrush}"
                                           HorizontalAlignment="Center"/>
                            </StackPanel>
                        </hc:Card>

                    </hc:UniformSpacingPanel>
                </Grid>
            </hc:TabItem>

            <!-- 设置页面 -->
            <hc:TabItem Header="设置" Style="{StaticResource TabItemInLine}">
                <ScrollViewer Margin="20">
                    <hc:UniformSpacingPanel Orientation="Vertical" Spacing="20">

                        <!-- 应用程序设置 -->
                        <hc:Card>
                            <StackPanel Margin="20">
                                <TextBlock Text="应用程序设置"
                                           FontSize="16"
                                           FontWeight="Bold"
                                           Margin="0,0,0,15"/>

                                <hc:UniformSpacingPanel Orientation="Horizontal" Spacing="20">
                                    <StackPanel>
                                        <TextBlock Text="主题选择" Margin="0,0,0,5"/>
                                        <hc:ComboBox Width="150" SelectedIndex="0">
                                            <ComboBoxItem Content="浅色主题"/>
                                            <ComboBoxItem Content="深色主题"/>
                                        </hc:ComboBox>
                                    </StackPanel>

                                    <StackPanel>
                                        <TextBlock Text="语言设置" Margin="0,0,0,5"/>
                                        <hc:ComboBox Width="150" SelectedIndex="0">
                                            <ComboBoxItem Content="简体中文"/>
                                            <ComboBoxItem Content="English"/>
                                        </hc:ComboBox>
                                    </StackPanel>
                                </hc:UniformSpacingPanel>

                                <CheckBox Content="启用自动保存"
                                          IsChecked="True"
                                          Margin="0,15,0,0"/>
                            </StackPanel>
                        </hc:Card>

                        <!-- 日志设置 -->
                        <hc:Card>
                            <StackPanel Margin="20">
                                <TextBlock Text="日志设置"
                                           FontSize="16"
                                           FontWeight="Bold"
                                           Margin="0,0,0,15"/>

                                <hc:UniformSpacingPanel Orientation="Horizontal" Spacing="20">
                                    <StackPanel>
                                        <TextBlock Text="日志级别" Margin="0,0,0,5"/>
                                        <hc:ComboBox Width="150" SelectedIndex="1">
                                            <ComboBoxItem Content="Debug"/>
                                            <ComboBoxItem Content="Information"/>
                                            <ComboBoxItem Content="Warning"/>
                                            <ComboBoxItem Content="Error"/>
                                        </hc:ComboBox>
                                    </StackPanel>

                                    <StackPanel>
                                        <TextBlock Text="保留天数" Margin="0,0,0,5"/>
                                        <hc:NumericUpDown Width="150"
                                                          Value="7"
                                                          Minimum="1"
                                                          Maximum="30"/>
                                    </StackPanel>
                                </hc:UniformSpacingPanel>
                            </StackPanel>
                        </hc:Card>

                    </hc:UniformSpacingPanel>
                </ScrollViewer>
            </hc:TabItem>

        </hc:TabControl>

        <!-- 状态栏 -->
        <Border Grid.Row="2"
                Background="{DynamicResource RegionBrush}"
                Height="30"
                BorderThickness="0,1,0,0"
                BorderBrush="{DynamicResource BorderBrush}">
            <TextBlock Text="{Binding StatusMessage}"
                       VerticalAlignment="Center"
                       Margin="10,0"
                       Foreground="{DynamicResource PrimaryTextBrush}"/>
        </Border>

    </Grid>
</Window>
