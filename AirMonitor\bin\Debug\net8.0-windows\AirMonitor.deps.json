{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"AirMonitor/1.0.0": {"dependencies": {"CommunityToolkit.Mvvm": "8.4.0", "HandyControl": "3.5.1", "Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Hosting": "9.0.6", "Serilog": "4.3.0", "Serilog.Enrichers.Environment": "3.0.1", "Serilog.Enrichers.Thread": "4.0.0", "Serilog.Extensions.Hosting": "9.0.0", "Serilog.Settings.Configuration": "9.0.0", "Serilog.Sinks.Debug": "3.0.0", "Serilog.Sinks.File": "7.0.0"}, "runtime": {"AirMonitor.dll": {}}}, "CommunityToolkit.Mvvm/8.4.0": {"runtime": {"lib/net8.0/CommunityToolkit.Mvvm.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "HandyControl/3.5.1": {"runtime": {"lib/net8.0/HandyControl.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Extensions.Configuration/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.CommandLine/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Physical": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.Json/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "System.Text.Json": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Physical": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.DependencyInjection/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.DependencyModel/9.0.0": {"dependencies": {"System.Text.Encodings.Web": "9.0.6", "System.Text.Json": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Diagnostics/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "System.Diagnostics.DiagnosticSource": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.6": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.FileSystemGlobbing": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.6": {"runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Hosting/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Binder": "9.0.6", "Microsoft.Extensions.Configuration.CommandLine": "9.0.6", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.6", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.Configuration.UserSecrets": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Diagnostics": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Physical": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Configuration": "9.0.6", "Microsoft.Extensions.Logging.Console": "9.0.6", "Microsoft.Extensions.Logging.Debug": "9.0.6", "Microsoft.Extensions.Logging.EventLog": "9.0.6", "Microsoft.Extensions.Logging.EventSource": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Hosting.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "System.Diagnostics.DiagnosticSource": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging.Configuration/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Binder": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging.Console/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Configuration": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "System.Text.Json": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging.Debug/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging.EventLog/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "System.Diagnostics.EventLog": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging.EventSource/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6", "System.Text.Json": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.EventSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Options/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Binder": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Primitives/9.0.6": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Serilog/4.3.0": {"runtime": {"lib/net8.0/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Enrichers.Environment/3.0.1": {"dependencies": {"Serilog": "4.3.0"}, "runtime": {"lib/net8.0/Serilog.Enrichers.Environment.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Enrichers.Thread/4.0.0": {"dependencies": {"Serilog": "4.3.0"}, "runtime": {"lib/net8.0/Serilog.Enrichers.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Hosting/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Serilog": "4.3.0", "Serilog.Extensions.Logging": "9.0.0"}, "runtime": {"lib/net8.0/Serilog.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Logging/9.0.0": {"dependencies": {"Microsoft.Extensions.Logging": "9.0.6", "Serilog": "4.3.0"}, "runtime": {"lib/net8.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Settings.Configuration/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.0", "Serilog": "4.3.0"}, "runtime": {"lib/net8.0/Serilog.Settings.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Debug/3.0.0": {"dependencies": {"Serilog": "4.3.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.File/7.0.0": {"dependencies": {"Serilog": "4.3.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.File.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Diagnostics.DiagnosticSource/9.0.6": {"runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Diagnostics.EventLog/9.0.6": {"runtime": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.IO.Pipelines/9.0.6": {"runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Text.Encodings.Web/9.0.6": {"runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Text.Json/9.0.6": {"dependencies": {"System.IO.Pipelines": "9.0.6", "System.Text.Encodings.Web": "9.0.6"}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}}}, "libraries": {"AirMonitor/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "CommunityToolkit.Mvvm/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-tqVU8yc/ADO9oiTRyTnwhFN68hCwvkliMierptWOudIAvWY1mWCh5VFh+guwHJmpMwfg0J0rY+yyd5Oy7ty9Uw==", "path": "communitytoolkit.mvvm/8.4.0", "hashPath": "communitytoolkit.mvvm.8.4.0.nupkg.sha512"}, "HandyControl/3.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-i2i0xrLev7F1MFnhf0DP1CNCdGw8hVJ0HJrI0kxRv02ZJgaAzLzDTgAXDPY8GAoD3aYnBLSgM74lBHZ844KQnQ==", "path": "handycontrol/3.5.1", "hashPath": "handycontrol.3.5.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-VWB5jdkxHsRiuoniTqwOL32R4OWyp5If/bAucLjRJczRVNcwb8iCXKLjn3Inv8fv+jHMVMnvQLg7xhSys+y5PA==", "path": "microsoft.extensions.configuration/9.0.6", "hashPath": "microsoft.extensions.configuration.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-3GgMIi2jP8g1fBW93Z9b9Unamc0SIsgyhiCmC91gq4loTixK9vQMuxxUsfJ1kRGwn+/FqLKwOHqmn0oYWn3Fvw==", "path": "microsoft.extensions.configuration.abstractions/9.0.6", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Opl/7SIrwDy9WjHn/vU2thQ8CUtrIWHLr+89I7/0VYNEJQvpL24zvqYrh83cH38RzNKHji0WGVkCVP6HJChVVw==", "path": "microsoft.extensions.configuration.binder/9.0.6", "hashPath": "microsoft.extensions.configuration.binder.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-DC5I4Y1nK35jY4piDqQCzWjDXzT6ECMctBAxgAJoc6pn0k6uyxcDeOuVDRooFui/N65ptn9xT5mk9eO4mSTj/g==", "path": "microsoft.extensions.configuration.commandline/9.0.6", "hashPath": "microsoft.extensions.configuration.commandline.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-RGYG2JBak9lf2rIPiZUVmWjUqoxaHPy3XPhPsJyIQ8QqK47rKvJz7jxVYefTnYdM5LTEiGFBdC7v3+SiosvmkQ==", "path": "microsoft.extensions.configuration.environmentvariables/9.0.6", "hashPath": "microsoft.extensions.configuration.environmentvariables.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-pCEueasI5JhJ24KYzMFxtG40zyLnWpcQYawpARh9FNq9XbWozuWgexmdkPa8p8YoVNlpi3ecKfcjfoRMkKAufw==", "path": "microsoft.extensions.configuration.fileextensions/9.0.6", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-N0dgOYQ9tDzJouL9Tyx2dgMCcHV2pBaY8yVtorbDqYYwiDRS2zd1TbhTA2FMHqXF3SMjBoO+gONZcDoA79gdSA==", "path": "microsoft.extensions.configuration.json/9.0.6", "hashPath": "microsoft.extensions.configuration.json.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-0ZZMzdvNwIS0f09S0IcaEbKFm+Xc41vRROsA/soeKEpzRISTDdiVwGlzdldbXEsuPjNVvNHyvIP8YW2hfIig0w==", "path": "microsoft.extensions.configuration.usersecrets/9.0.6", "hashPath": "microsoft.extensions.configuration.usersecrets.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-vS65HMo5RS10DD543fknsyVDxihMcVxVn3/hNaILgBxWYnOLxWIeCIO9X0QFuCvPRNjClvXe9Aj8KaQNx7vFkQ==", "path": "microsoft.extensions.dependencyinjection/9.0.6", "hashPath": "microsoft.extensions.dependencyinjection.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-0Zn6nR/6g+90MxskZyOOMPQvnPnrrGu6bytPwkV+azDcTtCSuQ1+GJUrg8Klmnrjk1i6zMpw2lXijl+tw7Q3kA==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.6", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-saxr2XzwgDU77LaQfYFXmddEDRUKHF4DaGMZkNB3qjdVSZlax3//dGJagJkKrGMIPNZs2jVFXITyCCR6UHJNdA==", "path": "microsoft.extensions.dependencymodel/9.0.0", "hashPath": "microsoft.extensions.dependencymodel.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-mIqCzZseDK9SqTRy4LxtjLwjlUu6aH5UdA6j0vgVER14yki9oRqLF+SmBiF6OlwsBSeL6dMQ8dmq02JMeE2puQ==", "path": "microsoft.extensions.diagnostics/9.0.6", "hashPath": "microsoft.extensions.diagnostics.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-GIoXX7VDcTEsNM6yvffTBaOwnPQELGI5dzExR7L2O7AUkDsHBYIZawUbuwfq3cYzz8dIAAJotQYJMzH7qy27Ng==", "path": "microsoft.extensions.diagnostics.abstractions/9.0.6", "hashPath": "microsoft.extensions.diagnostics.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-q9FPkSGVA9ipI255p3PBAvWNXas5Tzjyp/DwYSwT+46mIFw9fWZahsF6vHpoxLt5/vtANotH2sAm7HunuFIx9g==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.6", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-l+dFA0NRl90vSIiJNy5d7V0kpTEOWHTqbgoWYzlTwF5uiM5sWJ953haaELKE05jkyJdnemVTnqjrlgo4wo7oyg==", "path": "microsoft.extensions.fileproviders.physical/9.0.6", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-1HJ<PERSON>bwukNEoYbHgHbKHmenU0V/0huw8+i7Qtf5rLUG1E+3kEwRJQxpwD3wbTEagIgPSQisNgJTvmUX9yYVc6g==", "path": "microsoft.extensions.filesystemglobbing/9.0.6", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Hosting/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Iu1UyXUnjMhoOwThKM0kCyjgWqqQnuujsbPMnF44ITUbmETT7RAVlozNgev2L/damwNoPZKpmwArRKBy2IOAZg==", "path": "microsoft.extensions.hosting/9.0.6", "hashPath": "microsoft.extensions.hosting.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-G9T95JbcG/wQpeVIzg0IMwxI+uTywDmbxWUWN2P0mdna35rmuTqgTrZ4SU5rcfUT3EJfbI9N4K8UyCAAc6QK8Q==", "path": "microsoft.extensions.hosting.abstractions/9.0.6", "hashPath": "microsoft.extensions.hosting.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-XBzjitTFaQhF8EbJ645vblZezV1p52ePTxKHoVkRidHF11Xkjxg94qr0Rvp2qyxK2vBJ4OIZ41NB15YUyxTGMQ==", "path": "microsoft.extensions.logging/9.0.6", "hashPath": "microsoft.extensions.logging.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-LFnyBNK7WtFmKdnHu3v0HOYQ8BcjYuy0jdC9pgCJ/rbLKoJEG9/dBzSKMEeeWDbDeoWS0TIxOC8a9CM5ufca3A==", "path": "microsoft.extensions.logging.abstractions/9.0.6", "hashPath": "microsoft.extensions.logging.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-lCgpxE5r6v43SB40/yUVnSWZUUqUZF5iUWizhkx4gqvq0L0rMw5g8adWKGO7sfIaSbCiU0et85sDQWswhLcceg==", "path": "microsoft.extensions.logging.configuration/9.0.6", "hashPath": "microsoft.extensions.logging.configuration.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-L1O0M3MrqGlkrPYMLzcCphQpCG0lSHfTSPrm1otALNBzTPiO8rxxkjhBIIa2onKv92UP30Y4QaiigVMTx8YcxQ==", "path": "microsoft.extensions.logging.console/9.0.6", "hashPath": "microsoft.extensions.logging.console.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-u21euQdOjaEwmlnnB1Zd4XGqOmWI8FkoGeUleV7n4BZ8HPQC/jrYzX/B5Cz3uI/FXjd//W88clPfkGIbSif7Jw==", "path": "microsoft.extensions.logging.debug/9.0.6", "hashPath": "microsoft.extensions.logging.debug.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-IyyGy7xNJAjdlFYXc7SZ7kS3CWd3Ma4hing9QGtzXi+LXm8RWCEXdKA1cPx5AeFmdg3rVG+ADGIn44K14O+vFA==", "path": "microsoft.extensions.logging.eventlog/9.0.6", "hashPath": "microsoft.extensions.logging.eventlog.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-ayCRr/8ON3aINH81ak9l3vLAF/0pV/xrfChCbIlT2YnHAd4TYBWLcWhzbJWwPFV4XmJFrx/z8oq+gZzIc/74OA==", "path": "microsoft.extensions.logging.eventsource/9.0.6", "hashPath": "microsoft.extensions.logging.eventsource.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-wUPhNM1zsI58Dy10xRdF2+pnsisiUuETg5ZBncyAEEUm/CQ9Q1vmivyUWH8RDbAlqyixf2dJNQ2XZb7HsKUEQw==", "path": "microsoft.extensions.options/9.0.6", "hashPath": "microsoft.extensions.options.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-2lnp8nrvfzyp+5zvfeULm/hkZsDsKkl2ziBt5T8EZKoON5q+XRpRLoWcSPo8mP7GNZXpxKMBVjFNIZNbBIcnRw==", "path": "microsoft.extensions.options.configurationextensions/9.0.6", "hashPath": "microsoft.extensions.options.configurationextensions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-BHniU24QV67qp1pJknqYSofAPYGmijGI8D+ci9yfw33iuFdyOeB9lWTg78ThyYLyQwZw3s0vZ36VMb0MqbUuLw==", "path": "microsoft.extensions.primitives/9.0.6", "hashPath": "microsoft.extensions.primitives.9.0.6.nupkg.sha512"}, "Serilog/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+cDryFR0GRhsGOnZSKwaDzRRl4MupvJ42FhCE4zhQRVanX0Jpg6WuCBk59OVhVDPmab1bB+nRykAnykYELA9qQ==", "path": "serilog/4.3.0", "hashPath": "serilog.4.3.0.nupkg.sha512"}, "Serilog.Enrichers.Environment/3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-9BqCE4C9FF+/rJb/CsQwe7oVf44xqkOvMwX//CUxvUR25lFL4tSS6iuxE5eW07quby1BAyAEP+vM6TWsnT3iqw==", "path": "serilog.enrichers.environment/3.0.1", "hashPath": "serilog.enrichers.environment.3.0.1.nupkg.sha512"}, "Serilog.Enrichers.Thread/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-C7BK25a1rhUyr+Tp+1BYcVlBJq7M2VCHlIgnwoIUVJcicM9jYcvQK18+OeHiXw7uLPSjqWxJIp1EfaZ/RGmEwA==", "path": "serilog.enrichers.thread/4.0.0", "hashPath": "serilog.enrichers.thread.4.0.0.nupkg.sha512"}, "Serilog.Extensions.Hosting/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-u2TRxuxbjvTAldQn7uaAwePkWxTHIqlgjelekBtilAGL5sYyF3+65NWctN4UrwwGLsDC7c3Vz3HnOlu+PcoxXg==", "path": "serilog.extensions.hosting/9.0.0", "hashPath": "serilog.extensions.hosting.9.0.0.nupkg.sha512"}, "Serilog.Extensions.Logging/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NwSSYqPJeKNzl5AuXVHpGbr6PkZJFlNa14CdIebVjK3k/76kYj/mz5kiTRNVSsSaxM8kAIa1kpy/qyT9E4npRQ==", "path": "serilog.extensions.logging/9.0.0", "hashPath": "serilog.extensions.logging.9.0.0.nupkg.sha512"}, "Serilog.Settings.Configuration/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4/Et4Cqwa+F88l5SeFeNZ4c4Z6dEAIKbu3MaQb2Zz9F/g27T5a3wvfMcmCOaAiACjfUb4A6wrlTVfyYUZk3RRQ==", "path": "serilog.settings.configuration/9.0.0", "hashPath": "serilog.settings.configuration.9.0.0.nupkg.sha512"}, "Serilog.Sinks.Debug/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4BzXcdrgRX7wde9PmHuYd9U6YqycCC28hhpKonK7hx0wb19eiuRj16fPcPSVp0o/Y1ipJuNLYQ00R3q2Zs8FDA==", "path": "serilog.sinks.debug/3.0.0", "hashPath": "serilog.sinks.debug.3.0.0.nupkg.sha512"}, "Serilog.Sinks.File/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fKL7mXv7qaiNBUC71ssvn/dU0k9t0o45+qm2XgKAlSt19xF+ijjxyA3R6HmCgfKEKwfcfkwWjayuQtRueZFkYw==", "path": "serilog.sinks.file/7.0.0", "hashPath": "serilog.sinks.file.7.0.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-nikkwAKqpwWUvV5J8S9fnOPYg8k75Lf9fAI4bd6pyhyqNma0Py9kt+zcqXbe4TjJ4sTPcdYpPg81shYTrXnUZQ==", "path": "system.diagnostics.diagnosticsource/9.0.6", "hashPath": "system.diagnostics.diagnosticsource.9.0.6.nupkg.sha512"}, "System.Diagnostics.EventLog/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-lum+Dv+8S4gqN5H1C576UcQe0M2buoRjEUVs4TctXRSWjBH3ay3w2KyQrOo1yPdRs1I+xK69STz+4mjIisFI5w==", "path": "system.diagnostics.eventlog/9.0.6", "hashPath": "system.diagnostics.eventlog.9.0.6.nupkg.sha512"}, "System.IO.Pipelines/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-0nlr0reXrRmkZNKifKqh2DgGhQgfkT7Qa3gQxIn/JI7/y3WDiTz67M+Sq3vFhUqcG8O5zVrpqHvIHeGPGUBsEw==", "path": "system.io.pipelines/9.0.6", "hashPath": "system.io.pipelines.9.0.6.nupkg.sha512"}, "System.Text.Encodings.Web/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-uWRgViw2yJAUyGxrzDLCc6fkzE2dZIoXxs8V6YjCujKsJuP0pnpYSlbm2/7tKd0SjBnMtwfDQhLenk3bXonVOA==", "path": "system.text.encodings.web/9.0.6", "hashPath": "system.text.encodings.web.9.0.6.nupkg.sha512"}, "System.Text.Json/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-h+ZtYTyTnTh5Ju6mHCKb3FPGx4ylJZgm9W7Y2psUnkhQRPMOIxX+TCN0ZgaR/+Yea+93XHWAaMzYTar1/EHIPg==", "path": "system.text.json/9.0.6", "hashPath": "system.text.json.9.0.6.nupkg.sha512"}}}