using AirMonitor.Core;
using AirMonitor.Services;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;

namespace AirMonitor.ViewModels;

/// <summary>
/// 主窗口ViewModel
/// </summary>
public partial class MainViewModel : BaseViewModel
{
    public MainViewModel(ILogger<MainViewModel> logger) : base(logger)
    {
        Title = "AirMonitor - WPF MVVM 应用程序框架";
        StatusMessage = "应用程序已就绪";
    }
}
